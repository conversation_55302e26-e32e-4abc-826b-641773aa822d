current_dir=$(dirname "$(readlink -f "$0")")
cd $current_dir
export DOCKER_WORKPLACE_NAME=workplace_paper

export BASE_IMAGES=tjbtech1/paperagent:latest

export COMPLETION_MODEL="openai/gpt-4o-2024-08-06"
export CHEEP_MODEL="openai/gpt-4o-2024-08-06"
export API_BASE_URL="http://localhost:8321/v1"
export EMBEDDING_MODEL="/media/sc/AI/self-llm/embed_model/sentence-transformers/all-MiniLM-L6-v2"

python /media/sc/data/sc/AI-Researcher-v1.5/research_agent/run_infer_idea.py \
    --category "vq" \
    --instance_id fsq \
    --instance_path ../benchmark/final/${category}/${instance_id}.json \
    --model $COMPLETION_MODEL \
    --container_name "paper_eval" \
    --cache_path /media/sc/data/sc/AI-Researcher-v1.5/research_agent/cache \
    --max_iter_times 3 \
    --category ${category} \
    --workplace_name $DOCKER_WORKPLACE_NAME 